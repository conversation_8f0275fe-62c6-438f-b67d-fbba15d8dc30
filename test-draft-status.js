const axios = require('axios');

const API_BASE = 'http://localhost:3001/api';

// 测试数据
const testMaterial = {
  materialId: 12345,
  materialPubId: 67890,
  materialVersion: '1.0.0',
  namespace: 'test-namespace',
  schemaUrl: 'https://example.com/schema.json',
  result: {
    payload: {
      description: '这是一个测试组件的功能描述',
      propsDefine: 'interface Props { name: string; age: number; }',
      jsxDemo: ['<TestComponent name="test" age={25} />']
    }
  }
};

async function testDraftStatusLogic() {
  console.log('🧪 开始测试草稿状态逻辑...\n');

  try {
    // 1. 检查当前数据库中的描述数量
    console.log('1️⃣ 检查当前数据库状态...');
    const listResponse = await axios.get(`${API_BASE}/material-smart-description`, {
      params: { page: 1, pageSize: 10 }
    });
    
    if (listResponse.data.data && listResponse.data.data.list) {
      console.log(`   当前数据库中有 ${listResponse.data.data.list.length} 条记录`);
      
      // 显示现有记录的状态
      listResponse.data.data.list.forEach((item, index) => {
        console.log(`   记录 ${index + 1}: materialId=${item.materialId}, version=${item.materialVersion}, publishStatus=${item.publishStatus}`);
      });
    } else {
      console.log('   数据库中暂无记录');
    }

    // 2. 创建第一个智能描述（应该是已发布状态）
    console.log('\n2️⃣ 创建第一个智能描述（预期：已发布状态）...');
    const createResponse1 = await axios.post(`${API_BASE}/material-smart-description`, {
      jobId: 1,
      ...testMaterial
    });

    if (createResponse1.data.success) {
      const newRecord1 = createResponse1.data.data;
      console.log(`   ✅ 创建成功！ID: ${newRecord1.id}, publishStatus: ${newRecord1.publishStatus}`);
      console.log(`   预期：publishStatus = 1 (已发布)，实际：${newRecord1.publishStatus}`);
      
      if (newRecord1.publishStatus === 1) {
        console.log('   ✅ 第一个描述正确设置为已发布状态');
      } else {
        console.log('   ❌ 第一个描述应该是已发布状态，但实际不是');
      }
    } else {
      console.log('   ❌ 创建失败:', createResponse1.data.error);
      return;
    }

    // 3. 创建第二个智能描述（相同物料，不同版本，应该是草稿状态）
    console.log('\n3️⃣ 创建第二个智能描述（相同物料，新版本，预期：草稿状态）...');
    const testMaterial2 = {
      ...testMaterial,
      materialVersion: '1.1.0',
      result: {
        payload: {
          description: '这是版本1.1.0的功能描述',
          propsDefine: 'interface Props { name: string; age: number; enabled: boolean; }',
          jsxDemo: ['<TestComponent name="test" age={25} enabled={true} />']
        }
      }
    };

    const createResponse2 = await axios.post(`${API_BASE}/material-smart-description`, {
      jobId: 2,
      ...testMaterial2
    });

    if (createResponse2.data.success) {
      const newRecord2 = createResponse2.data.data;
      console.log(`   ✅ 创建成功！ID: ${newRecord2.id}, publishStatus: ${newRecord2.publishStatus}`);
      console.log(`   预期：publishStatus = 0 (草稿)，实际：${newRecord2.publishStatus}`);
      
      if (newRecord2.publishStatus === 0) {
        console.log('   ✅ 第二个描述正确设置为草稿状态');
      } else {
        console.log('   ❌ 第二个描述应该是草稿状态，但实际不是');
      }

      // 4. 测试草稿发布功能
      console.log('\n4️⃣ 测试草稿发布功能...');
      const publishResponse = await axios.post(`${API_BASE}/material-smart-description/${newRecord2.id}/publish`);
      
      if (publishResponse.data.success) {
        console.log('   ✅ 草稿发布成功！');
        
        // 验证发布后的状态
        const checkResponse = await axios.get(`${API_BASE}/material-smart-description/${newRecord2.id}`);
        if (checkResponse.data.success) {
          const updatedRecord = checkResponse.data.data;
          console.log(`   发布后状态：publishStatus = ${updatedRecord.publishStatus}`);
          
          if (updatedRecord.publishStatus === 1) {
            console.log('   ✅ 草稿成功转为已发布状态');
          } else {
            console.log('   ❌ 草稿发布后状态不正确');
          }
        }
      } else {
        console.log('   ❌ 草稿发布失败:', publishResponse.data.error);
      }
    } else {
      console.log('   ❌ 创建第二个描述失败:', createResponse2.data.error);
    }

    // 5. 最终状态检查
    console.log('\n5️⃣ 最终状态检查...');
    const finalListResponse = await axios.get(`${API_BASE}/material-smart-description`, {
      params: { page: 1, pageSize: 10 }
    });
    
    if (finalListResponse.data.data && finalListResponse.data.data.list) {
      console.log(`   最终数据库中有 ${finalListResponse.data.data.list.length} 条记录：`);
      finalListResponse.data.data.list.forEach((item, index) => {
        const statusText = item.publishStatus === 1 ? '已发布' : '草稿';
        console.log(`   记录 ${index + 1}: materialId=${item.materialId}, version=${item.materialVersion}, status=${statusText}`);
      });
    }

    console.log('\n🎉 测试完成！');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.response?.data || error.message);
  }
}

// 运行测试
testDraftStatusLogic();
