import React, { useState, useCallback, useEffect } from 'react'
import { Table, Button, Space, Typography, Pagination, Alert, Tag } from 'antd'
import { EyeOutlined, HistoryOutlined, DownOutlined, RightOutlined, CheckOutlined } from '@ant-design/icons'
import { message } from 'antd'
import { materialSmartDescriptionApi } from '../../../services/api'
import { getStateTag } from '@/constants/common'
import { formatTime, renderZhidaDescription } from './shared/utils'
import { DEFAULT_PAGE_SIZE } from './shared/constants'
import SearchFilters from './SearchFilters'
import ZhidaDescriptionEditor from './ZhidaDescriptionEditor'
import type {
  MaterialSmartDescription,
  MaterialGroupedDescription,
  DescriptionSearchParams,
  DescriptionListSectionProps,
} from './shared/types'
import styles from './MaterialSmartDescriptionList.module.less'

const { Text } = Typography

const DescriptionListSection: React.FC<DescriptionListSectionProps> = ({
  onViewDetail,
  onViewHistory,
}) => {
  // 列表数据状态
  const [data, setData] = useState<MaterialGroupedDescription[]>([])
  const [loading, setLoading] = useState(false)
  const [total, setTotal] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE)
  const [expandedRowKeys, setExpandedRowKeys] = useState<React.Key[]>([])

  // 搜索筛选状态
  const [searchParams, setSearchParams] = useState<Omit<DescriptionSearchParams, 'pageNum' | 'pageSize'>>({})

  // 编辑器模态框状态
  const [editorVisible, setEditorVisible] = useState(false)
  const [selectedRecord, setSelectedRecord] = useState<MaterialSmartDescription | null>(null)

  // 渲染发布状态标签
  const renderPublishStatus = (publishStatus: number) => {
    if (publishStatus === 0) {
      return <Tag color="orange">草稿</Tag>
    }
    return <Tag color="green">已发布</Tag>
  }

  // 处理编辑/发布草稿
  const handleEditOrPublishDraft = useCallback((record: MaterialSmartDescription) => {
    setSelectedRecord(record)
    setEditorVisible(true)
  }, [])

  // 关闭编辑器模态框
  const handleCloseEditor = useCallback(() => {
    setEditorVisible(false)
    setSelectedRecord(null)
  }, [])

  // 获取列表数据
  const fetchData = useCallback(async (params?: Partial<DescriptionSearchParams>) => {
    setLoading(true)
    try {
      const response = await materialSmartDescriptionApi.getDescriptions({
        pageNum: currentPage,
        pageSize,
        ...searchParams,
        ...params,
      })
      if (response.data.code === 1) {
        setData(response.data.data.list)
        setTotal(response.data.data.total)
      }
      else {
        message.error(response.data.message || '获取数据失败')
      }
    }
    catch (error) {
      console.error('获取数据失败:', error)
      message.error('获取数据失败，请稍后重试')
    }
    finally {
      setLoading(false)
    }
  }, [currentPage, pageSize, searchParams])

  // 编辑成功回调
  const handleEditSuccess = useCallback(() => {
    fetchData()
  }, [fetchData])

  // 分页变化处理
  const handlePageChange = useCallback((page: number, size?: number) => {
    if (size && size !== pageSize) {
      setPageSize(size)
      setCurrentPage(1)
    }
    else {
      setCurrentPage(page)
    }
  }, [pageSize])

  // 搜索处理
  const handleSearch = useCallback((params: Omit<DescriptionSearchParams, 'pageNum' | 'pageSize'>) => {
    setSearchParams(params)
    setCurrentPage(1) // 重置到第一页
  }, [])

  // 重置搜索
  const handleResetSearch = useCallback(() => {
    setSearchParams({})
    setCurrentPage(1)
  }, [])

  // 初始化数据
  useEffect(() => {
    fetchData({ pageNum: currentPage, pageSize })
  }, [currentPage, pageSize, searchParams, fetchData])

  // 展开/收起处理
  const handleExpand = (expanded: boolean, record: MaterialGroupedDescription) => {
    const key = record.materialId
    if (expanded) {
      setExpandedRowKeys([...expandedRowKeys, key])
    }
    else {
      setExpandedRowKeys(expandedRowKeys.filter(k => k !== key))
    }
  }

  // 子表格列定义（版本详情）
  const versionColumns = [
    {
      title: '版本',
      dataIndex: 'materialVersion',
      key: 'materialVersion',
      width: 120,
      render: (version: string) => (
        <Tag color="blue" style={{ fontSize: '12px' }}>
          {version}
        </Tag>
      ),
    },
    {
      title: 'ZhiDa 描述',
      dataIndex: 'zhidaDescription',
      key: 'zhidaDescription',
      width: 400,
      render: (_: unknown, record: MaterialSmartDescription) =>
        renderZhidaDescription(record.zhidaDescription),
    },
    {
      title: '创建时间',
      key: 'createTime',
      width: 180,
      render: (record: MaterialSmartDescription) => (
        <Text style={{ fontSize: '13px', color: '#595959' }}>
          {formatTime(record.createTime)}
        </Text>
      ),
    },
    {
      title: '状态',
      dataIndex: 'state',
      key: 'state',
      width: 80,
      render: getStateTag,
    },
    {
      title: '发布状态',
      dataIndex: 'publishStatus',
      key: 'publishStatus',
      width: 100,
      render: renderPublishStatus,
    },
    {
      title: '操作',
      key: 'actions',
      width: 160,
      render: (record: MaterialSmartDescription) => (
        <Space size={4} direction="vertical">
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => onViewDetail(record)}
            style={{
              padding: '2px 8px',
              height: 'auto',
              fontSize: '12px',
              color: '#1890ff',
            }}
          >
            详情
          </Button>
          <Button
            type="link"
            size="small"
            icon={<HistoryOutlined />}
            onClick={() => onViewHistory(record)}
            style={{
              padding: '2px 8px',
              height: 'auto',
              fontSize: '12px',
              color: '#722ed1',
            }}
          >
            历史
          </Button>
          {record.publishStatus === 0 && (
            <Button
              type="link"
              size="small"
              icon={<CheckOutlined />}
              onClick={() => handleEditOrPublishDraft(record)}
              style={{
                padding: '2px 8px',
                height: 'auto',
                fontSize: '12px',
                color: '#52c41a',
              }}
            >
              编辑/发布
            </Button>
          )}
        </Space>
      ),
    },
  ]

  // 主表格列定义（物料信息）
  const columns = [
    {
      title: '物料名称',
      key: 'title',
      width: 100,
      render: (record: MaterialGroupedDescription) => (
        <Text strong style={{ fontSize: '13px', color: '#262626' }}>
          {record.materialDetail.title}
        </Text>
      ),
    },
    {
      title: '组件名称',
      key: 'name',
      width: 100,
      render: (record: MaterialGroupedDescription) => (
        <Text code style={{ fontSize: '11px', background: '#e6f7ff', color: '#1890ff', padding: '2px 6px', borderRadius: '4px' }}>
          {record.materialDetail.name}
        </Text>
      ),
    },
    {
      title: '命名空间',
      key: 'namespace',
      width: 200,
      render: (record: MaterialGroupedDescription) => (
        <Text code style={{ fontSize: '11px', background: '#f6f8fa', color: '#666', padding: '2px 6px', borderRadius: '4px' }}>
          {record.materialDetail.namespace}
        </Text>
      ),
    },
    {
      title: '版本数',
      key: 'totalVersions',
      width: 80,
      align: 'center' as const,
      render: (record: MaterialGroupedDescription) => (
        <Tag color="green" style={{ fontSize: '11px', margin: 0 }}>
          {record.totalVersions}
        </Tag>
      ),
    },
    {
      title: '最新版本',
      key: 'latestVersion',
      width: 100,
      align: 'center' as const,
      render: (record: MaterialGroupedDescription) => (
        <Tag color="blue" style={{ fontSize: '11px', margin: 0 }}>
          {record.latestVersion}
        </Tag>
      ),
    },
    {
      title: '最新描述',
      key: 'latestDescription',
      width: 300,
      render: (record: MaterialGroupedDescription) => {
        const latestVersion = record.versions[0] // 已按时间排序，第一个是最新的
        return latestVersion ? renderZhidaDescription(latestVersion.zhidaDescription) : '-'
      },
    },
    {
      title: '操作',
      key: 'actions',
      width: 100,
      render: (record: MaterialGroupedDescription) => {
        const latestVersion = record.versions[0]
        return latestVersion
          ? (
            <Space size={2}>
              <Button
                type="link"
                size="small"
                icon={<EyeOutlined />}
                onClick={() => onViewDetail(latestVersion)}
                style={{
                  padding: '4px 6px',
                  height: 'auto',
                  fontSize: '11px',
                  color: '#1890ff',
                }}
              >
                详情
              </Button>
              <Button
                type="link"
                size="small"
                icon={<HistoryOutlined />}
                onClick={() => onViewHistory(latestVersion)}
                style={{
                  padding: '4px 6px',
                  height: 'auto',
                  fontSize: '11px',
                  color: '#722ed1',
                }}
              >
                历史
              </Button>
            </Space>
          )
          : null
      },
    },
  ]

  // 展开行渲染
  const expandedRowRender = (record: MaterialGroupedDescription) => {
    return (
      <Table
        columns={versionColumns}
        dataSource={record.versions}
        rowKey="id"
        pagination={false}
        size="small"
        style={{ margin: '0 48px' }}
      />
    )
  }

  return (
    <div>
      {/* 搜索筛选区域 */}
      <SearchFilters
        onSearch={handleSearch}
        onReset={handleResetSearch}
        loading={loading}
      />

      <div className={styles.searchSection}>
        <Alert
          message="数据说明"
          description="列表按物料分组显示，每行代表一个物料及其所有版本。点击展开按钮可查看该物料的所有版本详情。支持按命名空间、版本号、发布状态进行搜索筛选。"
          type="info"
          showIcon
          closable={false}
        />
      </div>

      <div className={styles.tableContainer}>
        <Table
          columns={columns}
          dataSource={data}
          rowKey="materialId"
          loading={loading}
          pagination={false}
          scroll={{ x: 1200 }}
          size="small"
          expandable={{
            expandedRowRender,
            onExpand: handleExpand,
            expandedRowKeys,
            expandIcon: ({ expanded, onExpand, record }) => (
              <Button
                type="text"
                size="small"
                icon={expanded ? <DownOutlined /> : <RightOutlined />}
                onClick={e => onExpand(record, e)}
                style={{ padding: '0 4px' }}
              />
            ),
          }}
          rowClassName={() => 'compact-row'}
        />

        <Pagination
          current={currentPage}
          pageSize={pageSize}
          total={total}
          showSizeChanger
          showQuickJumper
          showTotal={(total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条物料`}
          onChange={handlePageChange}
          onShowSizeChange={handlePageChange}
        />
      </div>

      {/* 编辑器模态框 */}
      <ZhidaDescriptionEditor
        visible={editorVisible}
        data={selectedRecord?.zhidaDescription || null}
        recordId={selectedRecord?.id || null}
        onCancel={handleCloseEditor}
        onSuccess={handleEditSuccess}
        isDraftMode={true}
        publishStatus={selectedRecord?.publishStatus}
      />
    </div>
  )
}

export default DescriptionListSection
