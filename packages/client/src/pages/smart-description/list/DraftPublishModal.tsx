import React, { useState, useEffect } from 'react'
import { Modal, Form, Input, Button, Space, message, Divider, Typography } from 'antd'
import { CheckOutlined, EditOutlined } from '@ant-design/icons'
import { materialSmartDescriptionApi } from '../../../services/api'
import type { MaterialSmartDescription, UpdateZhidaDescriptionDto } from '../../../services/api'

const { TextArea } = Input
const { Title, Text } = Typography

interface DraftPublishModalProps {
  visible: boolean
  record: MaterialSmartDescription | null
  onClose: () => void
  onSuccess: () => void
}

const DraftPublishModal: React.FC<DraftPublishModalProps> = ({
  visible,
  record,
  onClose,
  onSuccess,
}) => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [editMode, setEditMode] = useState(false)

  // 重置表单
  useEffect(() => {
    if (visible && record) {
      const zhidaDesc = record.zhidaDescription
      form.setFieldsValue({
        description: zhidaDesc?.description || '',
        propsDefine: zhidaDesc?.propsDefine || '',
        jsxDemo: zhidaDesc?.jsxDemo?.join('\n') || '',
      })
      setEditMode(false)
    }
  }, [visible, record, form])

  // 处理发布
  const handlePublish = async (values?: UpdateZhidaDescriptionDto) => {
    if (!record) return

    try {
      setLoading(true)

      const publishData = editMode && values ? { updateContent: values } : undefined

      await materialSmartDescriptionApi.publishDraftDescription(record.id, publishData)

      message.success(editMode ? '草稿已发布并更新内容' : '草稿已发布')
      onSuccess()
      onClose()
    }
    catch (error) {
      console.error('发布草稿失败:', error)
      message.error('发布草稿失败，请稍后重试')
    }
    finally {
      setLoading(false)
    }
  }

  // 直接发布（不修改内容）
  const handleDirectPublish = () => {
    handlePublish()
  }

  // 编辑并发布
  const handleEditAndPublish = () => {
    form.validateFields().then((values) => {
      // 处理 jsxDemo 字段
      const processedValues: UpdateZhidaDescriptionDto = {
        ...values,
        jsxDemo: values.jsxDemo ? values.jsxDemo.split('\n').filter((line: string) => line.trim()) : undefined,
      }
      handlePublish(processedValues)
    })
  }

  if (!record) return null

  return (
    <Modal
      title="发布草稿描述"
      open={visible}
      onCancel={onClose}
      width={800}
      footer={null}
    >
      <div style={{ marginBottom: 16 }}>
        <Text type="secondary">
          物料：
          {record.materialDetail?.title}
          {' '}
          (
          {record.materialDetail?.name}
          )
        </Text>
        <br />
        <Text type="secondary">
          版本：
          {record.materialVersion}
        </Text>
      </div>

      <Divider />

      <div style={{ marginBottom: 16 }}>
        <Title level={5}>发布选项</Title>
        <Space>
          <Button
            type="primary"
            icon={<CheckOutlined />}
            loading={loading && !editMode}
            onClick={handleDirectPublish}
          >
            直接发布
          </Button>
          <Button
            icon={<EditOutlined />}
            onClick={() => setEditMode(!editMode)}
          >
            {editMode ? '取消编辑' : '编辑后发布'}
          </Button>
        </Space>
      </div>

      {editMode && (
        <>
          <Divider />
          <Title level={5}>编辑内容</Title>
          <Form
            form={form}
            layout="vertical"
            onFinish={handleEditAndPublish}
          >
            <Form.Item
              label="功能描述"
              name="description"
            >
              <TextArea
                rows={3}
                placeholder="请输入功能描述"
              />
            </Form.Item>

            <Form.Item
              label="属性定义"
              name="propsDefine"
            >
              <TextArea
                rows={4}
                placeholder="请输入 TypeScript 类型定义"
                style={{ fontFamily: 'Monaco, Consolas, monospace' }}
              />
            </Form.Item>

            <Form.Item
              label="JSX 示例"
              name="jsxDemo"
              help="每行一个示例"
            >
              <TextArea
                rows={4}
                placeholder="请输入 JSX 示例，每行一个"
                style={{ fontFamily: 'Monaco, Consolas, monospace' }}
              />
            </Form.Item>

            <Form.Item>
              <Space>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading && editMode}
                  icon={<CheckOutlined />}
                >
                  编辑并发布
                </Button>
                <Button onClick={() => setEditMode(false)}>
                  取消
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </>
      )}
    </Modal>
  )
}

export default DraftPublishModal
