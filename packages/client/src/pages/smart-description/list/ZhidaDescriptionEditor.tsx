import React, { useState, useEffect } from 'react'
import {
  Modal,
  Form,
  Input,
  Button,
  message,
  Space,
  Card,
  Typography,
  Divider,
  Row,
  Col,
} from 'antd'
import { EditOutlined, SaveOutlined, CloseOutlined, CheckOutlined } from '@ant-design/icons'
import { materialSmartDescriptionApi } from '../../../services/api'
import type { ZhiDaNeededMaterialDescription, UpdateZhidaDescriptionDto } from '../../../services/api'

const { TextArea } = Input
const { Text } = Typography

interface ZhidaDescriptionEditorProps {
  visible: boolean
  data: ZhiDaNeededMaterialDescription | null
  recordId: number | null
  onCancel: () => void
  onSuccess: (updatedData: ZhiDaNeededMaterialDescription) => void
  // 新增：支持草稿发布模式
  isDraftMode?: boolean
  publishStatus?: number
}

/**
 * ZhiDa 描述编辑器
 * 支持编辑除 name 和 namespace 外的所有字段
 */
const ZhidaDescriptionEditor: React.FC<ZhidaDescriptionEditorProps> = ({
  visible,
  data,
  recordId,
  onCancel,
  onSuccess,
  isDraftMode = false,
  publishStatus,
}) => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [jsxDemoList, setJsxDemoList] = useState<string[]>([])
  const [childNestedList, setChildNestedList] = useState<string[]>([])
  const [publishAndSave, setPublishAndSave] = useState(false)

  // 初始化表单数据
  useEffect(() => {
    if (visible && data) {
      const formData = {
        description: data.description || '',
        propsDefine: data.propsDefine || '',
        jsxPropCompatible: data.jsxPropCompatible ? JSON.stringify(data.jsxPropCompatible, null, 2) : '',
        mergePropsBeforeInsert: data.mergePropsBeforeInsert ? JSON.stringify(data.mergePropsBeforeInsert, null, 2) : '',
        purePropEffect: data.purePropEffect !== undefined ? JSON.stringify(data.purePropEffect, null, 2) : '',
      }

      form.setFieldsValue(formData)
      setJsxDemoList(data.jsxDemo || [])
      setChildNestedList(data.childNested || [])
    }
  }, [visible, data, form])

  // 重置表单
  const resetForm = () => {
    form.resetFields()
    setJsxDemoList([])
    setChildNestedList([])
  }

  // 处理取消
  const handleCancel = () => {
    resetForm()
    onCancel()
  }

  // 添加 JSX 示例
  const addJsxDemo = () => {
    setJsxDemoList([...jsxDemoList, ''])
  }

  // 更新 JSX 示例
  const updateJsxDemo = (index: number, value: string) => {
    const newList = [...jsxDemoList]
    newList[index] = value
    setJsxDemoList(newList)
  }

  // 删除 JSX 示例
  const removeJsxDemo = (index: number) => {
    const newList = jsxDemoList.filter((_, i) => i !== index)
    setJsxDemoList(newList)
  }

  // 添加子组件
  const addChildNested = () => {
    setChildNestedList([...childNestedList, ''])
  }

  // 更新子组件
  const updateChildNested = (index: number, value: string) => {
    const newList = [...childNestedList]
    newList[index] = value
    setChildNestedList(newList)
  }

  // 删除子组件
  const removeChildNested = (index: number) => {
    const newList = childNestedList.filter((_, i) => i !== index)
    setChildNestedList(newList)
  }

  // 提交表单
  const handleSubmit = async (shouldPublish = false) => {
    if (!recordId) {
      message.error('记录 ID 不存在')
      return
    }

    try {
      const values = await form.validateFields()
      setLoading(true)
      setPublishAndSave(shouldPublish)

      // 构建更新数据
      const updateData: UpdateZhidaDescriptionDto = {
        description: values.description || undefined,
        propsDefine: values.propsDefine || undefined,
        jsxDemo: jsxDemoList.filter(item => item.trim()) || undefined,
        childNested: childNestedList.filter(item => item.trim()) || undefined,
      }

      // 处理 JSON 字段
      if (values.jsxPropCompatible?.trim()) {
        try {
          updateData.jsxPropCompatible = JSON.parse(values.jsxPropCompatible)
        }
        catch (_error) {
          message.error('JSX 属性兼容格式错误，请检查 JSON 格式')
          return
        }
      }

      if (values.mergePropsBeforeInsert?.trim()) {
        try {
          updateData.mergePropsBeforeInsert = JSON.parse(values.mergePropsBeforeInsert)
        }
        catch (_error) {
          message.error('插入前属性合并格式错误，请检查 JSON 格式')
          return
        }
      }

      if (values.purePropEffect?.trim()) {
        try {
          updateData.purePropEffect = JSON.parse(values.purePropEffect)
        }
        catch (_error) {
          message.error('属性二次适配格式错误，请检查 JSON 格式')
          return
        }
      }

      // 如果是草稿模式且需要发布，调用发布接口
      if (isDraftMode && shouldPublish) {
        const response = await materialSmartDescriptionApi.publishDraftDescription(recordId, {
          updateContent: updateData,
        })

        if (response.data.code === 1) {
          message.success('编辑并发布成功！')
          onSuccess(response.data.data.zhidaDescription)
          resetForm()
        }
        else {
          message.error(response.data.message || '发布失败')
        }
      }
      else {
        // 普通编辑模式，调用更新接口
        const response = await materialSmartDescriptionApi.updateZhidaDescription(recordId, updateData)

        if (response.data.code === 1) {
          message.success(isDraftMode ? '草稿保存成功！' : '保存成功！已创建新版本记录')
          onSuccess(response.data.data.zhidaDescription)
          resetForm()
        }
        else {
          message.error(response.data.message || '保存失败')
        }
      }
    }
    catch (error) {
      console.error('更新失败:', error)
      message.error('更新失败，请稍后重试')
    }
    finally {
      setLoading(false)
      setPublishAndSave(false)
    }
  }

  // 渲染底部按钮
  const renderFooter = () => {
    const buttons = [
      <Button key="cancel" onClick={handleCancel}>
        <CloseOutlined />
        取消
      </Button>
    ]

    if (isDraftMode && publishStatus === 0) {
      // 草稿模式：显示保存草稿和编辑并发布按钮
      buttons.push(
        <Button
          key="save"
          loading={loading && !publishAndSave}
          onClick={() => handleSubmit(false)}
        >
          <SaveOutlined />
          保存草稿
        </Button>
      )
      buttons.push(
        <Button
          key="publish"
          type="primary"
          loading={loading && publishAndSave}
          onClick={() => handleSubmit(true)}
        >
          <CheckOutlined />
          编辑并发布
        </Button>
      )
    }
    else {
      // 普通编辑模式：只显示保存按钮
      buttons.push(
        <Button
          key="save"
          type="primary"
          loading={loading}
          onClick={() => handleSubmit(false)}
        >
          <SaveOutlined />
          保存
        </Button>
      )
    }

    return <Space>{buttons}</Space>
  }

  return (
    <Modal
      title={(
        <Space>
          <EditOutlined style={{ color: '#1890ff' }} />
          <span style={{ fontSize: '16px', fontWeight: 600 }}>
            {isDraftMode && publishStatus === 0 ? '编辑草稿描述' : '编辑 ZhiDa 描述'}
          </span>
          {isDraftMode && publishStatus === 0 && (
            <span style={{
              fontSize: '12px',
              color: '#faad14',
              backgroundColor: '#fff7e6',
              padding: '2px 6px',
              borderRadius: '4px',
              border: '1px solid #ffd591'
            }}>
              草稿
            </span>
          )}
        </Space>
      )}
      open={visible}
      onCancel={handleCancel}
      width={800}
      footer={renderFooter()}
      styles={{
        body: { padding: '20px 24px' },
        header: { padding: '16px 24px 0' },
      }}
    >
      {data && (
        <div>
          {/* 不可编辑字段提示 */}
          <Card
            size="small"
            style={{
              marginBottom: 20,
              backgroundColor: '#f6f8fa',
              border: '1px solid #e1e4e8',
            }}
          >
            <Row gutter={16}>
              <Col span={8}>
                <Text strong style={{ color: '#595959' }}>物料名称: </Text>
                <Text style={{ color: '#1890ff' }}>{data.title}</Text>
              </Col>
              <Col span={8}>
                <Text strong style={{ color: '#595959' }}>组件名称: </Text>
                <Text code style={{ color: '#52c41a' }}>{data.name}</Text>
              </Col>
              <Col span={8}>
                <Text strong style={{ color: '#595959' }}>命名空间: </Text>
                <Text code>{data.namespace}</Text>
              </Col>
            </Row>
            <Divider style={{ margin: '12px 0' }} />
            <Space direction="vertical" size={4}>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                注意：物料名称（中文）、组件名称（英文）和命名空间来自物料平台，无法通过此界面修改
              </Text>
              <Text type="warning" style={{ fontSize: '12px' }}>
                💡 保存更改将创建一个新的版本记录，原记录将被保留作为历史版本
              </Text>
            </Space>
          </Card>

          <Form form={form} layout="vertical" size="middle">
            {/* 功能描述 */}
            <Form.Item
              label={(
                <span>
                  功能描述
                  <Text type="secondary" style={{ fontSize: '12px', fontWeight: 'normal', marginLeft: '4px' }}>
                    (description)
                  </Text>
                </span>
              )}
              name="description"
              tooltip="组件的功能描述"
            >
              <TextArea
                rows={3}
                placeholder="请输入组件的功能描述..."
                showCount
                maxLength={500}
              />
            </Form.Item>

            {/* 属性定义 */}
            <Form.Item
              label={(
                <span>
                  属性定义 (TypeScript)
                  <Text type="secondary" style={{ fontSize: '12px', fontWeight: 'normal', marginLeft: '4px' }}>
                    (propsDefine)
                  </Text>
                </span>
              )}
              name="propsDefine"
              tooltip="组件的 TypeScript 属性定义"
            >
              <TextArea
                rows={6}
                placeholder="interface Props {&#10;  name: string;&#10;  age?: number;&#10;}"
                style={{ fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace' }}
              />
            </Form.Item>

            {/* JSX 示例 */}
            <Form.Item
              label={(
                <span>
                  JSX 使用示例
                  <Text type="secondary" style={{ fontSize: '12px', fontWeight: 'normal', marginLeft: '4px' }}>
                    (jsxDemo)
                  </Text>
                </span>
              )}
              tooltip="组件的使用示例"
            >
              <div>
                {jsxDemoList.map((demo, index) => (
                  <div key={index} style={{ marginBottom: 8 }}>
                    <Row gutter={8}>
                      <Col flex="auto">
                        <TextArea
                          value={demo}
                          onChange={e => updateJsxDemo(index, e.target.value)}
                          placeholder={`示例 ${index + 1}: <MyComponent name="example" />`}
                          rows={2}
                          style={{ fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace' }}
                        />
                      </Col>
                      <Col>
                        <Button
                          type="text"
                          danger
                          onClick={() => removeJsxDemo(index)}
                          style={{ height: '100%' }}
                        >
                          删除
                        </Button>
                      </Col>
                    </Row>
                  </div>
                ))}
                <Button type="dashed" onClick={addJsxDemo} style={{ width: '100%' }}>
                  + 添加示例
                </Button>
              </div>
            </Form.Item>

            {/* 子组件嵌套 */}
            <Form.Item
              label={(
                <span>
                  子组件嵌套
                  <Text type="secondary" style={{ fontSize: '12px', fontWeight: 'normal', marginLeft: '4px' }}>
                    (childNested)
                  </Text>
                </span>
              )}
              tooltip="使用当前组件必须附带使用的组件，比如 Drawer 组件的 content 需要嵌套 DrawerContent 组件"
            >
              <div>
                {childNestedList.map((child, index) => (
                  <div key={index} style={{ marginBottom: 8 }}>
                    <Row gutter={8}>
                      <Col flex="auto">
                        <Input
                          value={child}
                          onChange={e => updateChildNested(index, e.target.value)}
                          placeholder={`子组件 ${index + 1}: Button`}
                        />
                      </Col>
                      <Col>
                        <Button
                          type="text"
                          danger
                          onClick={() => removeChildNested(index)}
                        >
                          删除
                        </Button>
                      </Col>
                    </Row>
                  </div>
                ))}
                <Button type="dashed" onClick={addChildNested} style={{ width: '100%' }}>
                  + 添加子组件
                </Button>
              </div>
            </Form.Item>

            <Divider>高级配置</Divider>

            {/* JSX 属性兼容 */}
            <Form.Item
              label={(
                <span>
                  JSX 属性兼容 (JSON)
                  <Text type="secondary" style={{ fontSize: '12px', fontWeight: 'normal', marginLeft: '4px' }}>
                    (jsxPropCompatible)
                  </Text>
                </span>
              )}
              name="jsxPropCompatible"
              tooltip="支持兼容物料生成数据层级错误，比如 Form 的内容在 items 中，但大模型可能返回内容在 children 内"
            >
              <TextArea
                rows={4}
                placeholder='{"className": "string", "style": "object"}'
                style={{ fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace' }}
              />
            </Form.Item>

            {/* 插入前属性合并 */}
            <Form.Item
              label={(
                <span>
                  插入前属性合并 (JSON)
                  <Text type="secondary" style={{ fontSize: '12px', fontWeight: 'normal', marginLeft: '4px' }}>
                    (mergePropsBeforeInsert)
                  </Text>
                </span>
              )}
              name="mergePropsBeforeInsert"
              tooltip="在插入组件之前需要合并的 props"
            >
              <TextArea
                rows={4}
                placeholder='{"defaultProps": {"theme": "default"}}'
                style={{ fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace' }}
              />
            </Form.Item>

            {/* 属性二次适配 */}
            <Form.Item
              label={(
                <span>
                  属性二次适配 (JSON)
                  <Text type="secondary" style={{ fontSize: '12px', fontWeight: 'normal', marginLeft: '4px' }}>
                    (purePropEffect)
                  </Text>
                </span>
              )}
              name="purePropEffect"
              tooltip="支持物料 prop 二次适配，比如 Card 组件判断 title 属性不存在时，将其 headStyle 需要设置为 { display: none }"
            >
              <TextArea
                rows={4}
                placeholder='[{"predicate": "props => !props.title", "alias": {"key": "headStyle", "value": {"display": "none"}}}]'
                style={{ fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace' }}
              />
            </Form.Item>
          </Form>
        </div>
      )}
    </Modal>
  )
}

export default ZhidaDescriptionEditor
